'use client';
import React, { useState } from 'react';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We will get back to you soon.');
    setFormData({ name: '', email: '', message: '' });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <main className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Title Section */}
        <h1 className="text-5xl font-bold text-center mb-4 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          Contact Us
        </h1>
        <p className="text-gray-300 text-center mb-12 max-w-4xl mx-auto">
          Get in touch with us for inquiries, support, or partnerships. Whether you have questions about the game, need assistance, or want to collaborate, we&apos;re here to help!
        </p>

        <div className="text-gray-300 text-center mb-12">
          <p>We&apos;d love to hear from you! Whether you have questions, feedback, or suggestions, feel free to reach out. We&apos;re here to assist and engage with our community.</p>
        </div>

        {/* Contact Methods Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text text-center">
            How to Contact Us
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">General Inquiries</h3>
              <p className="text-gray-300 mb-4">For any questions or general inquiries:</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Support</h3>
              <p className="text-gray-300 mb-4">Need help with the game?</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Partnerships</h3>
              <p className="text-gray-300 mb-4">For business opportunities:</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Content & Media</h3>
              <p className="text-gray-300 mb-4">For media inquiries and press matters:</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Social Media</h3>
              <p className="text-gray-300 mb-4">Connect with us:</p>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-purple-400 hover:text-purple-300 transition-colors">Facebook: Sprunki Final</a>
                </li>
                <li>
                  <a href="#" className="text-purple-400 hover:text-purple-300 transition-colors">Instagram: @sprunki.final</a>
                </li>
                <li>
                  <a href="#" className="text-purple-400 hover:text-purple-300 transition-colors">Twitter: @sprunki_final</a>
                </li>
              </ul>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Mailing Address</h3>
              <p className="text-gray-300">
                Sprunki Final<br />
                [Insert your mailing address here]
              </p>
            </div>
          </div>
        </div>

        {/* Contact Form Section */}
        <div className="max-w-2xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text text-center">
            Send Us a Message
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-900 border border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-900 border border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              />
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-1">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={6}
                className="w-full px-4 py-2 bg-gray-900 border border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              ></textarea>
            </div>

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-white py-3 px-6 rounded-md hover:opacity-90 transition-opacity"
            >
              Send Message
            </button>
          </form>
        </div>

        {/* Final Note Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            We&apos;re Here to Help
          </h2>
          <p className="text-gray-300 mb-4">
            Have a question, want to learn more about the game, or curious about upcoming updates? Don&apos;t hesitate to reach out to us! We&apos;ll respond as quickly as possible to ensure you have all the information you need.
          </p>
          <p className="text-gray-300">
            Thank you for visiting us. We look forward to hearing from you!
          </p>
        </div>
      </div>
    </main>
  );
}