import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import GoogleAnalytics from './components/GoogleAnalytics';
import GoogleAdsense from './components/GoogleAdsense';
import Script from 'next/script';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Sprunki Final: The Ultimate Rhythm Game That Will Blow Your Mind!",
  description: "Sprunki Final: The ultimate rhythm game with legendary beats, epic visuals, and creative music mixing. Play now on sprunki-final.com!",
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  alternates: {
    canonical: 'https://sprunki-final.com',
  },
  openGraph: {
    type: 'website',
    url: 'https://sprunki-final.com',
    title: 'Sprunki Final: The Ultimate Rhythm Game',
    description: 'Experience the next level of rhythm gaming with Sprunki Final! Immerse yourself in dynamic beats, stunning visuals, and limitless creative possibilities.',
    siteName: 'Sprunki Final',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sprunki Final: The Ultimate Rhythm Game',
    description: 'Experience the next level of rhythm gaming with Sprunki Final!',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <Script
          defer
          data-domain="sprunki-final.com"
          src="https://plausible.nancook.com/js/script.file-downloads.hash.outbound-links.pageview-props.tagged-events.js"
          strategy="beforeInteractive"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        {/*<HeadScript />*/}
        <GoogleAnalytics />
        <GoogleAdsense />
        <Navbar />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
