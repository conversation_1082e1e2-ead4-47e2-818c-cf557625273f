import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-[80vh] flex items-center justify-center bg-black text-white">
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          404 - Page Not Found
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Oops! It seems you&apos;ve ventured into unknown territory. Don&apos;t worry, you can always return to the rhythm and continue your musical journey.
        </p>
        <div className="space-y-4">
          <Link
            href="/"
            className="inline-block bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-white py-3 px-8 rounded-md hover:opacity-90 transition-all font-medium text-lg"
          >
            Return to Homepage
          </Link>
          <p className="text-gray-400 mt-6">
            Want to explore more? Check out our{' '}
            <Link href="/" className="text-purple-400 hover:text-purple-300 transition-colors underline">
              latest games
            </Link>
            {' '}or{' '}
            <Link href="/contact" className="text-purple-400 hover:text-purple-300 transition-colors underline">
              contact us
            </Link>
            {' '}for help.
          </p>
        </div>
      </div>
    </div>
  );
}
