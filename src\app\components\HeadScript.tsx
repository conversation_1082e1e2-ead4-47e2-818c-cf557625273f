'use client'
import { useEffect } from 'react';

export default function HeadScript() {
    useEffect(() => {
        (function(d,z,s){
            s.src='https://'+d+'/401/'+z;
            try{
                (document.body||document.documentElement).appendChild(s)
            }catch{
                // Silently handle any errors during script injection
            }
        })('groleegni.net',9090236,document.createElement('script'))
    }, []);

    return null;
} 