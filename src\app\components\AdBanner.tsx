'use client'
import { useEffect, useState } from 'react';

export default function AdBanner() {
    const [adWidth, setAdWidth] = useState(300);
    const [adHeight, setAdHeight] = useState(250);

    useEffect(() => {
        const updateWidth = () => {
            setAdWidth(300);
            setAdHeight(250);
        };
        updateWidth();
    }, []);

    return (
        <div className="flex justify-center my-4">
            <iframe
                src={`/adsterra_${adWidth}.html`}
                width={`${adWidth}px`}
                height={`${adHeight}px`}
                scrolling="no"
                style={{
                    border: 'none',
                    overflow: 'hidden'
                }}
            />
        </div>
    );
} 