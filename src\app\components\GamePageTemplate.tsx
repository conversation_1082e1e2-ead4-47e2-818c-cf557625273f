import { useState } from 'react';
import Script from 'next/script';
import Image from 'next/image';

interface GamePageTemplateProps {
  title: string;
  description: string;
  gameUrl: string;
  gameTitle: string;
  gameDescription: string;
  phaseGames: {
    title: string;
    image: string;
  }[];
  videos: {
    title: string;
    videoId: string;
  }[];
  gameFeatures: {
    title: string;
    description: string;
  }[];
  exploreGames: {
    title: string;
    description: string;
    features: string[];
    image: string;
    order: 'normal' | 'reverse';
  }[];
  howToPlay: {
    title: string;
    description: string;
  }[];
  story: {
    title: string;
    description: string[];
    imagePath: string;
  };
  gamesCollection: {
    title: string;
    votes: number;
    overview: {
      title: string;
      description: string;
    };
    features: {
      title: string;
      items: string[];
    };
    image: string;
    order: 'normal' | 'reverse';
  }[];
  soundOfCorruption: {
    title: string;
    description: string;
  }[];
  visualEvolution: {
    title: string;
    description: string;
    image: string;
  }[];
  experience: {
    forCreators: string[];
    forEnthusiasts: string[];
  };
  about: string;
}

export default function GamePageTemplate({
  title,
  description,
  gameUrl,
  gameTitle,
  gameDescription,
  phaseGames,
  videos,
  gameFeatures,
  exploreGames,
  howToPlay,
  story,
  gamesCollection,
  soundOfCorruption,
  visualEvolution,
  experience,
  about
}: GamePageTemplateProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = () => {
    const gameContainer = document.getElementById('game-container');
    if (!document.fullscreenElement) {
      gameContainer?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <main className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Title Section */}
        <h1 className="text-5xl font-bold text-center mb-4 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          {title}
        </h1>
        <p className="text-gray-300 text-center mb-8">
          {description}
        </p>

        {/* Game Container */}
        <div className="relative w-full mx-auto aspect-[16/9] bg-gray-900 rounded-lg overflow-hidden mb-8 border-2 border-purple-600 shadow-[0_0_15px_rgba(147,51,234,0.5)] p-1" id="game-container">
          <iframe
            src={gameUrl}
            className="w-full h-full border-0 rounded-lg"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
          <button
            onClick={toggleFullscreen}
            className="absolute bottom-4 right-4 bg-black/40 backdrop-blur-sm text-white/90 px-4 py-2 rounded-md hover:bg-black/60 transition-all font-medium"
          >
            {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
          </button>
        </div>

        {/* Game Title */}
        <h2 className="text-2xl font-bold text-center mb-2">{gameTitle}</h2>
        <p className="text-gray-400 text-center mb-8">
          {gameDescription}
        </p>

        {/* Share Buttons */}
        <div className="max-w-2xl mx-auto mb-16">
          <Script
            type="text/javascript"
            src="https://platform-api.sharethis.com/js/sharethis.js#property=67668519d5bedc001907e655&product=inline-share-buttons"
            async
          />
          <div className="sharethis-inline-share-buttons"></div>
        </div>

        {/* Game Showcase Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Sprunki Phase Games
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {phaseGames.map((game, index) => (
              <div key={index} className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 hover:border-purple-600 transition-colors group">
                <div className="aspect-video relative bg-purple-900/20">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-center text-white group-hover:text-purple-400 transition-colors">
                    {game.title}
                  </h3>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-6">
            <a href="#" className="text-purple-400 hover:text-purple-300 transition-colors">
              See All Sprunki Mods
            </a>
          </div>
        </div>

        {/* Video Showcase Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Sprunki Mod Videos
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {videos.map((video, index) => (
              <div key={index} className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 hover:border-purple-600 transition-colors">
                <div className="aspect-video relative">
                  <iframe
                    src={`https://www.youtube.com/embed/${video.videoId}`}
                    className="absolute inset-0 w-full h-full"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-center text-white hover:text-purple-400 transition-colors">
                    {video.title}
                  </h3>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Game Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {gameFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <h3 className="text-xl font-bold mb-4 text-purple-400">{feature.title}</h3>
                <p className="text-gray-400">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Explore Games Section */}
        <div className="mb-16">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            Explore Our Games
          </h2>
          {exploreGames.map((game, index) => (
            <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${index !== exploreGames.length - 1 ? 'mb-24' : ''}`}>
              {game.order === 'normal' ? (
                <>
                  <div className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30">
                    <Image 
                      src={game.image}
                      alt={game.title}
                      width={1920}
                      height={1080}
                      className="w-full aspect-[16/9] object-cover"
                    />
                  </div>
                  <div className="flex flex-col justify-center">
                    <h3 className="text-3xl font-bold mb-6 text-pink-500">{game.title}</h3>
                    <p className="text-gray-300 mb-6">
                      {game.description}
                    </p>
                    <ul className="space-y-3 text-gray-400">
                      {game.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center">
                          <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex flex-col justify-center order-2 lg:order-1">
                    <h3 className="text-3xl font-bold mb-6 text-pink-500">{game.title}</h3>
                    <p className="text-gray-300 mb-6">
                      {game.description}
                    </p>
                    <ul className="space-y-3 text-gray-400">
                      {game.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center">
                          <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 order-1 lg:order-2">
                    <Image 
                      src={game.image}
                      alt={game.title}
                      width={1920}
                      height={1080}
                      className="w-full aspect-[16/9] object-cover"
                    />
                  </div>
                </>
              )}
            </div>
          ))}
        </div>

        {/* How to Play Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            How to Play
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {howToPlay.map((step, index) => (
              <div key={index} className="text-center">
                <h3 className="text-2xl font-bold mb-4 text-purple-400">{step.title}</h3>
                <p className="text-gray-400">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Story Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            {story.title}
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30">
              <Image 
                src={story.imagePath}
                alt={story.title}
                width={1920}
                height={1080}
                className="w-full aspect-[16/9] object-cover"
              />
            </div>
            <div>
              {story.description.map((paragraph, index) => (
                <p key={index} className="text-gray-300 mb-6">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        </div>

        {/* Our Games Collection Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            Our Games Collection
          </h2>
          
          {gamesCollection.map((game, index) => (
            <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${index !== gamesCollection.length - 1 ? 'mb-16' : ''}`}>
              {game.order === 'normal' ? (
                <>
                  <div className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30">
                    <Image 
                      src={game.image}
                      alt={game.title}
                      width={1920}
                      height={1080}
                      className="w-full aspect-[16/9] object-cover"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-3xl font-bold text-pink-500">{game.title}</h3>
                      <span className="text-gray-400">{game.votes} votes</span>
                    </div>
                    <div className="mb-6">
                      <h4 className="text-xl font-semibold mb-2 text-purple-400">{game.overview.title}</h4>
                      <p className="text-gray-300">
                        {game.overview.description}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-xl font-semibold mb-2 text-purple-400">{game.features.title}</h4>
                      <ul className="space-y-2 text-gray-400">
                        {game.features.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="order-2 lg:order-1">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-3xl font-bold text-pink-500">{game.title}</h3>
                      <span className="text-gray-400">{game.votes} votes</span>
                    </div>
                    <div className="mb-6">
                      <h4 className="text-xl font-semibold mb-2 text-purple-400">{game.overview.title}</h4>
                      <p className="text-gray-300">
                        {game.overview.description}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-xl font-semibold mb-2 text-purple-400">{game.features.title}</h4>
                      <ul className="space-y-2 text-gray-400">
                        {game.features.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 order-1 lg:order-2">
                    <Image 
                      src={game.image}
                      alt={game.title}
                      width={1920}
                      height={1080}
                      className="w-full aspect-[16/9] object-cover"
                    />
                  </div>
                </>
              )}
            </div>
          ))}
        </div>

        {/* The Sound of Corruption Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            The Sound of Corruption
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {soundOfCorruption.map((item, index) => (
              <div key={index}>
                <h3 className="text-2xl font-bold mb-4 text-purple-400">{item.title}</h3>
                <p className="text-gray-400">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Visual Evolution Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            Visual Evolution
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {visualEvolution.map((item, index) => (
              <div key={index} className="bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30">
                <Image 
                  src={item.image}
                  alt={item.title}
                  width={1920}
                  height={1080}
                  className="w-full aspect-[16/9] object-cover"
                />
                <div className="p-4">
                  <h3 className="text-xl font-bold text-center text-purple-400 mb-2">{item.title}</h3>
                  <p className="text-gray-400 text-center text-sm">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* The Corruptbox Experience Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            The Corruptbox Experience
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <div>
              <h3 className="text-2xl font-bold mb-6 text-purple-400">For Music Creators</h3>
              <ul className="space-y-4 text-gray-400">
                {experience.forCreators.map((item, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-2xl font-bold mb-6 text-purple-400">For Horror Enthusiasts</h3>
              <ul className="space-y-4 text-gray-400">
                {experience.forEnthusiasts.map((item, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* About Corruptbox Section */}
        <div className="mb-24">
          <h2 className="text-4xl font-bold text-center mb-16 text-pink-500">
            About Corruptbox
          </h2>
          <p className="text-gray-300 text-center max-w-4xl mx-auto">
            {about}
          </p>
        </div>
      </div>
    </main>
  );
} 