import Link from 'next/link';
import Image from 'next/image';

export default function Navbar() {
  return (
    <nav className="w-full bg-black text-white shadow-md">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Image
            src="/logo.png"
            alt="Sprunki Final Logo"
            width={100}
            height={100}
            className="w-16 h-16 rounded-md"
          />
          <span className="text-xl font-bold text-white">Sprunki Final</span>
        </Link>
        
        <div className="hidden md:flex space-x-6">
          <Link href="/" className="text-white hover:text-purple-400 transition-colors">
            Home
          </Link>
          {/*
          <Link href="/blog" className="text-white hover:text-purple-400 transition-colors">
            Blog
          </Link>
          */}
          <Link href="/about-us" className="text-white hover:text-purple-400 transition-colors">
            About us
          </Link>
          <Link href="/contact" className="text-white hover:text-purple-400 transition-colors">
            Contact
          </Link>
          <Link href="/privacy-policy" className="text-white hover:text-purple-400 transition-colors">
            Privacy Policy
          </Link>
        </div>

        <button className="md:hidden text-white">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </nav>
  );
}