{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pages:build": "npx @cloudflare/next-on-pages", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv env.d.ts"}, "dependencies": {"gray-matter": "^4.0.3", "markdown-it": "^14.1.0", "next": "15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "remark-html": "^16.0.1", "remark-parse": "^11.0.0", "unified": "^11.0.5"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.7", "@cloudflare/workers-types": "^4.20250214.0", "@eslint/eslintrc": "^3", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vercel": "^41.1.4", "wrangler": "^3.109.1"}}