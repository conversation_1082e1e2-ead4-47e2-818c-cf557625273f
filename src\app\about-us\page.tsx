import React from 'react';
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "About Us - Sprunki Final",
  description: "Learn about Sprunki Final, your ultimate destination for rhythm gaming, character customization, and immersive audiovisual experiences.",
  alternates: {
    canonical: 'https://sprunki-final.com/about-us',
  },
};

export default function AboutUs() {
  return (
    <main className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Title Section */}
        <h1 className="text-5xl font-bold text-center mb-4 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          About Us
        </h1>
        <p className="text-gray-300 text-center mb-12 max-w-4xl mx-auto">
          Welcome to Sprunki Final, your ultimate destination for rhythm gaming, character customization, and immersive audiovisual experiences. Dive into the world of creative gameplay and join our community of passionate players.
        </p>

        {/* Welcome Section */}
        <div className="mb-16 text-gray-300">
          <p className="mb-6">
            Welcome to <span className="text-purple-400 font-semibold">Sprunki Final</span> – your go-to place for everything related to rhythm gaming, customization, and dynamic audiovisual experiences!
          </p>
          <p className="mb-6">
            At <span className="text-purple-400 font-semibold">Sprunki Final</span>, we are passionate about bringing you the most exciting and creative experiences in rhythm games. Whether you&apos;re a seasoned player or a newcomer, we aim to provide a platform where players can explore, create, and connect. Our focus is on offering engaging content that helps you unlock new gameplay features and immerse yourself in a world of music, visuals, and fun.
          </p>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Our Mission
          </h2>
          <p className="text-gray-300">
            Our mission is to provide a comprehensive resource for players, helping you understand the game&apos;s mechanics, discover new ways to personalize your characters, and enjoy the immersive world of rhythm gaming. We strive to connect players from around the world, offering valuable insights and tips to enhance your experience.
          </p>
        </div>

        {/* What We Offer Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            What We Offer
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                title: "Gameplay Guides",
                description: "In-depth tutorials and strategies to help you master the game and improve your skills."
              },
              {
                title: "Character Customization",
                description: "Learn how to customize your in-game characters and explore all the creative possibilities."
              },
              {
                title: "Immersive Visuals",
                description: "Discover how the game's stunning visuals respond dynamically to music and enhance your experience."
              },
              {
                title: "Community Events",
                description: "Join our growing community and stay updated with the latest news, features, and events."
              },
              {
                title: "Fun Content",
                description: "Articles, tips, and exclusive content to keep you engaged and entertained."
              }
            ].map((item, index) => (
              <div key={index} className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
                <h3 className="text-xl font-bold mb-4 text-purple-400">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Why Choose Us
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Expert Insights</h3>
              <p className="text-gray-300">
                We provide clear, detailed information that helps you make the most of your gameplay experience.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Creative Exploration</h3>
              <p className="text-gray-300">
                Our content encourages you to experiment with customization and gameplay, unlocking your creativity.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Global Community</h3>
              <p className="text-gray-300">
                Connect with players worldwide, share your experiences, and join in the excitement of rhythm gaming.
              </p>
            </div>
          </div>
        </div>

        {/* Get Involved Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Get Involved
          </h2>
          <p className="text-gray-300 mb-6">
            If you&apos;re ready to dive deeper into the world of rhythm games and enhance your gameplay experience, explore our site! Here, you&apos;ll find guides, tips, and a vibrant community that&apos;s always ready to share and learn together.
          </p>
          <p className="text-gray-300">
            Thank you for visiting <span className="text-purple-400 font-semibold">Sprunki Final</span>—your destination for rhythm, creativity, and fun!
          </p>
        </div>

        {/* Contact Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Contact Us
          </h2>
          <p className="text-gray-300 mb-4">
            Have any questions or feedback? We&apos;d love to hear from you!
          </p>
          <ul className="text-gray-300">
            <li className="mb-2">
              <span className="text-purple-400">Email:</span>{" "}
              <a href="mailto:<EMAIL>" className="hover:text-purple-400 transition-colors">
                <EMAIL>
              </a>
            </li>
            <li>
              <span className="text-purple-400">Social Media:</span> Follow us for updates and news about the game and community events.
            </li>
          </ul>
        </div>
      </div>
    </main>
  );
}